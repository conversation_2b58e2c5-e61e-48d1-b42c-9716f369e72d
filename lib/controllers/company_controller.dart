import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:myrunway/core/models/company_model.dart';
import 'package:myrunway/core/services/company_service.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class CompanyController extends GetxController {
  final CompanyService _companyService = Get.find<CompanyService>();
  final AuthService _authService = Get.find<AuthService>();

  // Reactive variables
  final RxList<CompanyModel> _companies = <CompanyModel>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isCreating = false.obs;
  final RxBool _isUpdating = false.obs;
  final RxBool _isDeleting = false.obs;
  final Rx<CompanyModel?> _selectedCompany = Rx<CompanyModel?>(null);

  // Getters
  List<CompanyModel> get companies => _companies;
  bool get isLoading => _isLoading.value;
  bool get isCreating => _isCreating.value;
  bool get isUpdating => _isUpdating.value;
  bool get isDeleting => _isDeleting.value;
  CompanyModel? get selectedCompany => _selectedCompany.value;

  // Permission checks
  bool get canManageCompanies {
    final userRole = _authService.currentUserRole;
    return userRole != null && userRole.canManageClients;
  }

  bool get canCreateCompanies => canManageCompanies;
  bool get canEditCompanies => canManageCompanies;
  bool get canDeleteCompanies => canManageCompanies;

  @override
  void onInit() {
    super.onInit();
    if (canManageCompanies) {
      loadCompanies();
    }
  }

  // Load all companies
  Future<void> loadCompanies() async {
    if (!canManageCompanies) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لعرض الشركات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    _isLoading.value = true;

    final response = await _companyService.getCompanies();

    if (response.success && response.data != null) {
      _companies.value = response.data!;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في جلب قائمة الشركات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoading.value = false;
  }

  // Create a new company
  Future<bool> createCompany(CompanyCreateRequest request) async {
    if (!canCreateCompanies) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لإنشاء شركة جديدة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    _isCreating.value = true;

    final response = await _companyService.createCompany(request);

    if (response.success && response.data != null) {
      _companies.add(response.data!);
      Get.snackbar(
        'نجح',
        'تم إنشاء الشركة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      _isCreating.value = false;
      return true;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في إنشاء الشركة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      _isCreating.value = false;
      return false;
    }
  }

  // Update an existing company
  Future<bool> updateCompany(int companyId, CompanyEditRequest request) async {
    if (!canEditCompanies) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لتعديل الشركات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    _isUpdating.value = true;

    final response = await _companyService.updateCompany(companyId, request);

    if (response.success && response.data != null) {
      final index = _companies.indexWhere((company) => company.id == companyId);
      if (index != -1) {
        _companies[index] = response.data!;
      }
      Get.snackbar(
        'نجح',
        'تم تحديث الشركة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      _isUpdating.value = false;
      return true;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في تحديث الشركة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      _isUpdating.value = false;
      return false;
    }
  }

  // Delete a company
  Future<bool> deleteCompany(int companyId) async {
    if (!canDeleteCompanies) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لحذف الشركات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    _isDeleting.value = true;

    final response = await _companyService.deleteCompany(companyId);

    if (response.success) {
      _companies.removeWhere((company) => company.id == companyId);
      Get.snackbar(
        'نجح',
        'تم حذف الشركة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      _isDeleting.value = false;
      return true;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في حذف الشركة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      _isDeleting.value = false;
      return false;
    }
  }

  // Select a company
  void selectCompany(CompanyModel company) {
    _selectedCompany.value = company;
  }

  // Clear selection
  void clearSelection() {
    _selectedCompany.value = null;
  }

  // Refresh companies list
  Future<void> refreshCompanies() async {
    await loadCompanies();
  }

  // Get company by ID
  CompanyModel? getCompanyById(int id) {
    try {
      return _companies.firstWhere((company) => company.id == id);
    } catch (e) {
      return null;
    }
  }
}
