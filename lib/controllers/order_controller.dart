import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/services/order_service.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class OrderController extends GetxController {
  final OrderService _orderService = Get.find<OrderService>();
  final AuthService _authService = Get.find<AuthService>();

  // Reactive variables
  final RxList<OrderModelNew> _orders = <OrderModelNew>[].obs;
  final RxList<OrderModelNew> _myOrders = <OrderModelNew>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isCreating = false.obs;
  final RxBool _isUpdating = false.obs;
  final RxBool _isDeleting = false.obs;
  final RxBool _isAssigning = false.obs;
  final Rx<OrderModelNew?> _selectedOrder = Rx<OrderModelNew?>(null);

  // Getters
  List<OrderModelNew> get orders => _orders;
  List<OrderModelNew> get myOrders => _myOrders;
  bool get isLoading => _isLoading.value;
  bool get isCreating => _isCreating.value;
  bool get isUpdating => _isUpdating.value;
  bool get isDeleting => _isDeleting.value;
  bool get isAssigning => _isAssigning.value;
  OrderModelNew? get selectedOrder => _selectedOrder.value;

  // Permission checks
  bool get canManageOrders {
    final userRole = _authService.currentUserRole;
    return userRole != null && userRole.canManageOrders;
  }

  bool get canCreateOrders => canManageOrders;
  bool get canEditOrders => canManageOrders;
  bool get canDeleteOrders => canManageOrders;
  bool get canAssignOrders => canManageOrders;
  bool get canViewAllOrders => canManageOrders;

  @override
  void onInit() {
    super.onInit();
    if (canViewAllOrders) {
      loadOrders();
    } else {
      loadMyOrders();
    }
  }

  // Load all orders (for managers and masters)
  Future<void> loadOrders({
    String? dateFrom,
    String? dateTo,
    String? status,
    int? assignedTo,
  }) async {
    if (!canViewAllOrders) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لعرض جميع الطلبات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    _isLoading.value = true;

    final response = await _orderService.getOrders(
      dateFrom: dateFrom,
      dateTo: dateTo,
      status: status,
      assignedTo: assignedTo,
    );

    if (response.success && response.data != null) {
      _orders.value = response.data!;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في جلب قائمة الطلبات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoading.value = false;
  }

  // Load current user's orders (for employees)
  Future<void> loadMyOrders({
    String? dateFrom,
    String? dateTo,
    String? status,
  }) async {
    _isLoading.value = true;

    final response = await _orderService.getMyOrders(
      dateFrom: dateFrom,
      dateTo: dateTo,
      status: status,
    );

    if (response.success && response.data != null) {
      _myOrders.value = response.data!;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في جلب طلباتك',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoading.value = false;
  }

  // Create a new order
  Future<bool> createOrder(OrderCreateRequest request) async {
    if (!canCreateOrders) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لإنشاء طلب جديد',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    _isCreating.value = true;

    final response = await _orderService.createOrder(request);

    if (response.success && response.data != null) {
      _orders.add(response.data!);
      Get.snackbar(
        'نجح',
        'تم إنشاء الطلب بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      _isCreating.value = false;
      return true;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في إنشاء الطلب',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      _isCreating.value = false;
      return false;
    }
  }

  // Update an existing order
  Future<bool> updateOrder(int orderId, OrderCreateRequest request) async {
    if (!canEditOrders) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لتعديل الطلبات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    _isUpdating.value = true;

    final response = await _orderService.updateOrder(orderId, request);

    if (response.success && response.data != null) {
      final index = _orders.indexWhere((order) => order.id == orderId);
      if (index != -1) {
        _orders[index] = response.data!;
      }
      Get.snackbar(
        'نجح',
        'تم تحديث الطلب بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      _isUpdating.value = false;
      return true;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في تحديث الطلب',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      _isUpdating.value = false;
      return false;
    }
  }

  // Delete an order
  Future<bool> deleteOrder(int orderId) async {
    if (!canDeleteOrders) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لحذف الطلبات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    _isDeleting.value = true;

    final response = await _orderService.deleteOrder(orderId);

    if (response.success) {
      _orders.removeWhere((order) => order.id == orderId);
      _myOrders.removeWhere((order) => order.id == orderId);
      Get.snackbar(
        'نجح',
        'تم حذف الطلب بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      _isDeleting.value = false;
      return true;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في حذف الطلب',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      _isDeleting.value = false;
      return false;
    }
  }

  // Assign order to employee
  Future<bool> assignOrder(int orderId, int employeeId) async {
    if (!canAssignOrders) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لتعيين الطلبات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    _isAssigning.value = true;

    final request = OrderAssignRequest(employeeId: employeeId);
    final response = await _orderService.assignOrder(orderId, request);

    if (response.success && response.data != null) {
      final index = _orders.indexWhere((order) => order.id == orderId);
      if (index != -1) {
        _orders[index] = response.data!;
      }
      Get.snackbar(
        'نجح',
        'تم تعيين الطلب بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      _isAssigning.value = false;
      return true;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في تعيين الطلب',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      _isAssigning.value = false;
      return false;
    }
  }

  // Select an order
  void selectOrder(OrderModelNew order) {
    _selectedOrder.value = order;
  }

  // Clear selection
  void clearSelection() {
    _selectedOrder.value = null;
  }

  // Refresh orders
  Future<void> refreshOrders() async {
    if (canViewAllOrders) {
      await loadOrders();
    } else {
      await loadMyOrders();
    }
  }

  // Get order by ID
  OrderModelNew? getOrderById(int id) {
    try {
      return _orders.firstWhere((order) => order.id == id);
    } catch (e) {
      try {
        return _myOrders.firstWhere((order) => order.id == id);
      } catch (e) {
        return null;
      }
    }
  }
}
