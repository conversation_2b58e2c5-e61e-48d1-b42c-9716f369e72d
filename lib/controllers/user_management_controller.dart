import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:myrunway/core/models/user_model.dart';
import 'package:myrunway/core/services/user_management_service.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class UserManagementController extends GetxController {
  final UserManagementService _userService = Get.find<UserManagementService>();
  final AuthService _authService = Get.find<AuthService>();

  // Reactive variables
  final RxList<UserModel> _users = <UserModel>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isCreating = false.obs;
  final RxBool _isUpdating = false.obs;
  final RxBool _isDeleting = false.obs;
  final Rx<UserModel?> _selectedUser = Rx<UserModel?>(null);

  // Getters
  List<UserModel> get users => _users;
  bool get isLoading => _isLoading.value;
  bool get isCreating => _isCreating.value;
  bool get isUpdating => _isUpdating.value;
  bool get isDeleting => _isDeleting.value;
  UserModel? get selectedUser => _selectedUser.value;

  // Permission checks
  bool get canViewUsers {
    final userRole = _authService.currentUserRole;
    return userRole != null && userRole.canManageEmployees;
  }

  bool get canCreateUsers {
    final userRole = _authService.currentUserRole;
    return userRole == UserRole.master; // Only master can create users
  }

  bool get canEditUsers {
    final userRole = _authService.currentUserRole;
    return userRole != null && userRole.canManageEmployees;
  }

  bool get canDeleteUsers {
    final userRole = _authService.currentUserRole;
    return userRole != null && userRole.canManageEmployees;
  }

  @override
  void onInit() {
    super.onInit();
    if (canViewUsers) {
      loadUsers();
    }
  }

  // Load all office users
  Future<void> loadUsers() async {
    if (!canViewUsers) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لعرض المستخدمين',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    _isLoading.value = true;

    final response = await _userService.getOfficeUsers();

    if (response.success && response.data != null) {
      _users.value = response.data!;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في جلب قائمة المستخدمين',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoading.value = false;
  }

  // Create a new user
  Future<bool> createUser(UserCreateRequest request) async {
    if (!canCreateUsers) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لإنشاء مستخدم جديد',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    _isCreating.value = true;

    final response = await _userService.createUser(request);

    if (response.success && response.data != null) {
      _users.add(response.data!);
      Get.snackbar(
        'نجح',
        'تم إنشاء المستخدم بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      _isCreating.value = false;
      return true;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في إنشاء المستخدم',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      _isCreating.value = false;
      return false;
    }
  }

  // Update an existing user
  Future<bool> updateUser(int userId, UserEditRequest request) async {
    if (!canEditUsers) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لتعديل المستخدمين',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    _isUpdating.value = true;

    final response = await _userService.updateUser(userId, request);

    if (response.success && response.data != null) {
      final index = _users.indexWhere((user) => user.id == userId);
      if (index != -1) {
        _users[index] = response.data!;
      }
      Get.snackbar(
        'نجح',
        'تم تحديث المستخدم بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      _isUpdating.value = false;
      return true;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في تحديث المستخدم',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      _isUpdating.value = false;
      return false;
    }
  }

  // Delete a user
  Future<bool> deleteUser(int userId) async {
    if (!canDeleteUsers) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لحذف المستخدمين',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    // Prevent deleting current user
    if (_authService.currentUser?.id == userId) {
      Get.snackbar(
        'خطأ',
        'لا يمكنك حذف حسابك الخاص',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    _isDeleting.value = true;

    final response = await _userService.deleteUser(userId);

    if (response.success) {
      _users.removeWhere((user) => user.id == userId);
      Get.snackbar(
        'نجح',
        'تم حذف المستخدم بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      _isDeleting.value = false;
      return true;
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في حذف المستخدم',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      _isDeleting.value = false;
      return false;
    }
  }

  // Select a user
  void selectUser(UserModel user) {
    _selectedUser.value = user;
  }

  // Clear selection
  void clearSelection() {
    _selectedUser.value = null;
  }

  // Refresh users list
  Future<void> refreshUsers() async {
    await loadUsers();
  }

  // Get user by ID
  UserModel? getUserById(int id) {
    try {
      return _users.firstWhere((user) => user.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get users by role
  List<UserModel> getUsersByRole(UserRole role) {
    return _users.where((user) => user.role == role).toList();
  }

  // Get employees only
  List<UserModel> get employees => getUsersByRole(UserRole.employee);

  // Get managers only
  List<UserModel> get managers => getUsersByRole(UserRole.manager);

  // Get masters only
  List<UserModel> get masters => getUsersByRole(UserRole.master);
}
