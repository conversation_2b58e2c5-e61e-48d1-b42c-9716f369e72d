class ApiEndpoints {
  // Base URL - Update this with your actual API base URL
  static const String baseUrl = 'https://245b9v2j-8000.uks1.devtunnels.ms';
  static const String apiBase = '$baseUrl/api';

  // Authentication Endpoints
  static const String login = '$apiBase/accounts/';
  static const String getCurrentUser = '$apiBase/accounts/me';
  static const String createUser = '$apiBase/accounts/create';
  static const String editUser = '$apiBase/accounts/edit'; // + /{user_id}
  static const String deleteUser = '$apiBase/accounts/delete'; // + /{user_id}
  static const String updateLocation = '$apiBase/accounts/update-location';
  static const String getEmployeeStats =
      '$apiBase/accounts/employees'; // + /{employee_id}/stats/

  // User Endpoints
  static const String profile = '$apiBase/user/profile';
  static const String updateProfile = '$apiBase/user/profile';
  static const String changePassword = '$apiBase/user/change-password';

  // Orders Endpoints
  static const String orders = '$apiBase/orders';
  static const String createOrder = '$apiBase/orders';
  static const String updateOrder = '$apiBase/orders'; // + /{id}
  static const String deleteOrder = '$apiBase/orders'; // + /{id}
  static const String assignOrder = '$apiBase/orders'; // + /{id}/assign
  static const String updateOrderStatus = '$apiBase/orders'; // + /{id}/status
  static const String orderProofOfDelivery = '$apiBase/orders'; // + /{id}/proof

  // Employees Endpoints
  static const String employees = '$apiBase/employees';
  static const String createEmployee = '$apiBase/employees';
  static const String updateEmployee = '$apiBase/employees'; // + /{id}
  static const String deleteEmployee = '$apiBase/employees'; // + /{id}
  static const String employeeOrders = '$apiBase/employees'; // + /{id}/orders
  static const String employeePerformance =
      '$apiBase/employees'; // + /{id}/performance

  // Companies Endpoints
  static const String companies = '$apiBase/companies';
  static const String createCompany = '$apiBase/companies/';
  static const String updateCompany = '$apiBase/companies'; // + /{id}/
  static const String deleteCompany = '$apiBase/companies'; // + /{id}/

  // Office Users Endpoints
  static const String officeUsers = '$apiBase/offices/users';

  // Offices Endpoints
  static const String offices = '$apiBase/offices';
  static const String createOffice = '$apiBase/offices';
  static const String updateOffice = '$apiBase/offices'; // + /{id}
  static const String deleteOffice = '$apiBase/offices'; // + /{id}
  static const String officeEmployees = '$apiBase/offices'; // + /{id}/employees
  static const String officeOrders = '$apiBase/offices'; // + /{id}/orders

  // Reports Endpoints
  static const String reportsRevenue = '$apiBase/reports/revenue';
  static const String reportsCommission = '$apiBase/reports/commission';
  static const String reportsDeliveryMetrics =
      '$apiBase/reports/delivery-metrics';
  static const String reportsEmployeePerformance =
      '$apiBase/reports/employee-performance';
  static const String reportsOverallMetrics =
      '$apiBase/reports/overall-metrics';

  // Dashboard Endpoints
  static const String dashboardEmployee = '$apiBase/dashboard/employee';
  static const String dashboardManager = '$apiBase/dashboard/manager';
  static const String dashboardMaster = '$apiBase/dashboard/master';

  // File Upload Endpoints
  static const String uploadImage = '$apiBase/upload/image';
  static const String uploadDocument = '$apiBase/upload/document';

  // Helper methods for dynamic endpoints
  static String orderById(String id) => '$orders/$id/';
  static String employeeById(String id) => '$employees/$id';
  static String companyById(String id) => '$companies/$id/';
  static String officeById(String id) => '$offices/$id';
  static String userById(String id) => '$editUser/$id';

  static String assignOrderToEmployee(String orderId) =>
      '$orders/$orderId/assign/';
  static String transferOrder(String orderId) => '$orders/$orderId/transfer/';
  static String completeOrder(String orderId) => '$orders/$orderId/complete/';
  static String orderProofById(String orderId) => '$orders/$orderId/proof/';

  static String employeeOrdersById(String employeeId) =>
      '$employees/$employeeId/orders';
  static String employeePerformanceById(String employeeId) =>
      '$employees/$employeeId/performance';
  static String employeeStatsById(String employeeId) =>
      '$getEmployeeStats/$employeeId/stats/';

  static String officeEmployeesById(String officeId) =>
      '$offices/$officeId/employees';
  static String officeOrdersById(String officeId) =>
      '$offices/$officeId/orders';
}
