import 'package:get/get.dart';
import 'package:myrunway/core/models/user_model.dart';
import 'package:myrunway/core/constants/api_endpoints.dart';
import 'package:myrunway/core/services/api_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class UserManagementService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();

  // Get all office users
  Future<ApiResponse<List<UserModel>>> getOfficeUsers() async {
    final response = await _apiService.get<List<dynamic>>(
      ApiEndpoints.officeUsers,
      (data) => data as List<dynamic>,
    );

    if (response.success && response.data != null) {
      final users = response.data!
          .map((json) => UserModel.fromJson(json as Map<String, dynamic>))
          .toList();
      return ApiResponse.success(users);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في جلب قائمة المستخدمين',
      statusCode: response.statusCode,
    );
  }

  // Create a new user
  Future<ApiResponse<UserModel>> createUser(
    UserCreateRequest request,
  ) async {
    final response = await _apiService.post<Map<String, dynamic>>(
      ApiEndpoints.createUser,
      (data) => data as Map<String, dynamic>,
      body: request.toJson(),
    );

    if (response.success && response.data != null) {
      final user = UserModel.fromJson(response.data!);
      return ApiResponse.success(user);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في إنشاء المستخدم',
      statusCode: response.statusCode,
      errors: response.errors,
    );
  }

  // Update an existing user
  Future<ApiResponse<UserModel>> updateUser(
    int userId,
    UserEditRequest request,
  ) async {
    if (request.isEmpty) {
      return ApiResponse.error('لا توجد بيانات للتحديث');
    }

    final response = await _apiService.put<Map<String, dynamic>>(
      ApiEndpoints.userById(userId.toString()),
      (data) => data as Map<String, dynamic>,
      body: request.toJson(),
    );

    if (response.success && response.data != null) {
      final user = UserModel.fromJson(response.data!);
      return ApiResponse.success(user);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في تحديث المستخدم',
      statusCode: response.statusCode,
      errors: response.errors,
    );
  }

  // Delete a user
  Future<ApiResponse<bool>> deleteUser(int userId) async {
    final response = await _apiService.delete<Map<String, dynamic>>(
      '${ApiEndpoints.deleteUser}/$userId',
      (data) => data as Map<String, dynamic>,
    );

    if (response.success) {
      return ApiResponse.success(true);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في حذف المستخدم',
      statusCode: response.statusCode,
    );
  }

  // Get user by ID
  Future<ApiResponse<UserModel>> getUserById(int userId) async {
    final response = await _apiService.get<Map<String, dynamic>>(
      ApiEndpoints.userById(userId.toString()),
      (data) => data as Map<String, dynamic>,
    );

    if (response.success && response.data != null) {
      final user = UserModel.fromJson(response.data!);
      return ApiResponse.success(user);
    }

    return ApiResponse.error(
      response.message ?? 'فشل في جلب بيانات المستخدم',
      statusCode: response.statusCode,
    );
  }
}

// Request models for user management
class UserCreateRequest {
  final String username;
  final String password;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String phone;
  final UserRole role;
  final double? commissionRate;

  UserCreateRequest({
    required this.username,
    required this.password,
    this.firstName,
    this.lastName,
    this.email,
    required this.phone,
    this.role = UserRole.employee,
    this.commissionRate,
  });

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'password': password,
      'first_name': firstName,
      'last_name': lastName,
      'email': email,
      'phone': phone,
      'role': role.name,
      'commission_rate': commissionRate,
    };
  }
}

class UserEditRequest {
  final String? username;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? phone;
  final UserRole? role;
  final double? commissionRate;

  UserEditRequest({
    this.username,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.role,
    this.commissionRate,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    
    if (username != null) data['username'] = username;
    if (firstName != null) data['first_name'] = firstName;
    if (lastName != null) data['last_name'] = lastName;
    if (email != null) data['email'] = email;
    if (phone != null) data['phone'] = phone;
    if (role != null) data['role'] = role!.name;
    if (commissionRate != null) data['commission_rate'] = commissionRate;
    
    return data;
  }

  bool get isEmpty => 
      username == null && 
      firstName == null && 
      lastName == null && 
      email == null && 
      phone == null && 
      role == null && 
      commissionRate == null;
}
