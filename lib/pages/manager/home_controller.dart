import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:myrunway/controllers/user_controller.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/pages/companies/companies_list_page.dart';
import 'package:myrunway/pages/orders/orders_list_page.dart';
import 'package:myrunway/pages/users/users_list_page.dart';

class ManagerHomeController extends GetxController {
  final UserController userController = Get.find<UserController>();
  final AuthService authService = Get.find<AuthService>();

  final RxBool isLoading = false.obs;
  final RxList<Map<String, dynamic>> recentActivities =
      <Map<String, dynamic>>[].obs;
  final RxMap<String, int> quickStats = <String, int>{}.obs;

  @override
  void onInit() {
    super.onInit();
    loadDashboardData();
  }

  Future<void> loadDashboardData() async {
    isLoading.value = true;

    // Simulate API calls
    await Future.delayed(const Duration(seconds: 1));

    // Load quick stats
    quickStats.value = {
      'todayOrders': 45,
      'activeEmployees': 12,
      'completedOrders': 38,
      'todayRevenue': 15250,
    };

    // Load recent activities
    recentActivities.value = [
      {
        'id': '1',
        'type': 'order_delivered',
        'title': 'تم تسليم الطلب #ORD-001',
        'subtitle': 'بواسطة أحمد محمد',
        'timestamp': DateTime.now().subtract(const Duration(minutes: 5)),
        'icon': Icons.check_circle,
        'color': Colors.green,
      },
      {
        'id': '2',
        'type': 'new_order',
        'title': 'طلب جديد #ORD-045',
        'subtitle': 'من عميل جديد',
        'timestamp': DateTime.now().subtract(const Duration(minutes: 15)),
        'icon': Icons.assignment,
        'color': Colors.blue,
      },
      {
        'id': '3',
        'type': 'new_employee',
        'title': 'موظف جديد انضم للفريق',
        'subtitle': 'سارة أحمد',
        'timestamp': DateTime.now().subtract(const Duration(hours: 1)),
        'icon': Icons.person_add,
        'color': Colors.orange,
      },
    ];

    isLoading.value = false;
  }

  Future<void> refreshDashboard() async {
    await loadDashboardData();
    Get.snackbar(
      'تم التحديث',
      'تم تحديث بيانات لوحة التحكم',
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  // Navigation methods
  void navigateToOrders() {
    Get.to(() => const OrdersListPage());
  }

  void navigateToEmployees() {
    Get.to(() => const UsersListPage());
  }

  void navigateToClients() {
    Get.to(() => const CompaniesListPage());
  }

  void navigateToReports() {
    Get.snackbar(
      'قريباً',
      'سيتم تطوير صفحة التقارير قريباً',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
    // TODO: Navigate to reports page
    // Get.to(() => ManagerReportsPage());
  }

  void navigateToSettings() {
    Get.snackbar(
      'قريباً',
      'سيتم تطوير صفحة الإعدادات قريباً',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
    // TODO: Navigate to settings page
    // Get.to(() => ManagerSettingsPage());
  }

  // Quick action methods
  void createNewOrder() {
    Get.snackbar(
      'قريباً',
      'سيتم تطوير إنشاء طلب جديد قريباً',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
    // TODO: Navigate to create order page
  }

  void assignOrder() {
    Get.snackbar(
      'قريباً',
      'سيتم تطوير تعيين الطلبات قريباً',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
    // TODO: Navigate to assign order page
  }

  void addEmployee() {
    Get.snackbar(
      'قريباً',
      'سيتم تطوير إضافة موظف جديد قريباً',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
    // TODO: Navigate to add employee page
  }

  void viewReports() {
    navigateToReports();
  }

  // Data getters
  int get todayOrders => quickStats['todayOrders'] ?? 0;
  int get activeEmployees => quickStats['activeEmployees'] ?? 0;
  int get completedOrders => quickStats['completedOrders'] ?? 0;
  int get todayRevenue => quickStats['todayRevenue'] ?? 0;

  double get completionRate {
    if (todayOrders == 0) return 0.0;
    return (completedOrders / todayOrders) * 100;
  }

  String get formattedRevenue {
    return '${todayRevenue.toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')} ر.س';
  }

  // Activity helpers
  String formatActivityTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  Color getActivityColor(String type) {
    switch (type) {
      case 'order_delivered':
        return Colors.green;
      case 'new_order':
        return Colors.blue;
      case 'new_employee':
        return Colors.orange;
      case 'order_cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData getActivityIcon(String type) {
    switch (type) {
      case 'order_delivered':
        return Icons.check_circle;
      case 'new_order':
        return Icons.assignment;
      case 'new_employee':
        return Icons.person_add;
      case 'order_cancelled':
        return Icons.cancel;
      default:
        return Icons.info;
    }
  }
}
