import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:myrunway/controllers/user_management_controller.dart';
import 'package:myrunway/core/services/user_management_service.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/core/constants/user_roles.dart';
import 'package:myrunway/utils/form_validators.dart';

class UserCreatePage extends StatefulWidget {
  const UserCreatePage({super.key});

  @override
  State<UserCreatePage> createState() => _UserCreatePageState();
}

class _UserCreatePageState extends State<UserCreatePage> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _commissionRateController = TextEditingController();

  final UserManagementController _controller =
      Get.find<UserManagementController>();

  UserRole _selectedRole = UserRole.employee;
  bool _obscurePassword = true;

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _commissionRateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة مستخدم جديد'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Username
              TextFormField(
                controller: _usernameController,
                decoration: const InputDecoration(
                  labelText: 'اسم المستخدم *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.person),
                  helperText: 'يجب أن يكون فريداً',
                ),
                validator: FormValidators.required,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),

              // Password
              TextFormField(
                controller: _passwordController,
                decoration: InputDecoration(
                  labelText: 'كلمة المرور *',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.lock),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscurePassword
                          ? Icons.visibility
                          : Icons.visibility_off,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                  ),
                  helperText: 'يجب أن تكون 8 أحرف على الأقل',
                ),
                obscureText: _obscurePassword,
                validator: FormValidators.validatePassword,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),

              // First Name
              TextFormField(
                controller: _firstNameController,
                decoration: const InputDecoration(
                  labelText: 'الاسم الأول',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.person_outline),
                ),
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),

              // Last Name
              TextFormField(
                controller: _lastNameController,
                decoration: const InputDecoration(
                  labelText: 'اسم العائلة',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.person_outline),
                ),
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),

              // Email
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(
                  labelText: 'البريد الإلكتروني',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.email),
                ),
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    return FormValidators.validateEmail(value);
                  }
                  return null;
                },
                keyboardType: TextInputType.emailAddress,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),

              // Phone
              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'رقم الهاتف *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.phone),
                ),
                validator: FormValidators.phone,
                keyboardType: TextInputType.phone,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),

              // Role Selection
              DropdownButtonFormField<UserRole>(
                value: _selectedRole,
                decoration: const InputDecoration(
                  labelText: 'الدور *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.admin_panel_settings),
                ),
                items: [
                  DropdownMenuItem(
                    value: UserRole.manager,
                    child: Text(UserRole.manager.displayName),
                  ),
                  DropdownMenuItem(
                    value: UserRole.employee,
                    child: Text(UserRole.employee.displayName),
                  ),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedRole = value!;
                    // Clear commission rate if not employee
                    if (_selectedRole != UserRole.employee) {
                      _commissionRateController.clear();
                    }
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'يرجى اختيار الدور';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Commission Rate (only for employees)
              if (_selectedRole == UserRole.employee) ...[
                TextFormField(
                  controller: _commissionRateController,
                  decoration: const InputDecoration(
                    labelText: 'نسبة العمولة',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.percent),
                    suffixText: '%',
                    helperText: 'اختياري - نسبة العمولة للموظف',
                  ),
                  validator: (value) {
                    if (value != null && value.isNotEmpty) {
                      final rate = double.tryParse(value);
                      if (rate == null) {
                        return 'يرجى إدخال رقم صحيح';
                      }
                      if (rate < 0 || rate > 100) {
                        return 'النسبة يجب أن تكون بين 0 و 100';
                      }
                    }
                    return null;
                  },
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(
                      RegExp(r'^\d+\.?\d{0,2}'),
                    ),
                  ],
                  textInputAction: TextInputAction.done,
                ),
                const SizedBox(height: 16),
              ],

              const SizedBox(height: 16),

              // Create Button
              Obx(
                () => ElevatedButton(
                  onPressed: _controller.isCreating ? null : _createUser,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child:
                      _controller.isCreating
                          ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                          : const Text(
                            'إنشاء المستخدم',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _createUser() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final request = UserCreateRequest(
      username: _usernameController.text.trim(),
      password: _passwordController.text.trim(),
      firstName:
          _firstNameController.text.trim().isEmpty
              ? null
              : _firstNameController.text.trim(),
      lastName:
          _lastNameController.text.trim().isEmpty
              ? null
              : _lastNameController.text.trim(),
      email:
          _emailController.text.trim().isEmpty
              ? null
              : _emailController.text.trim(),
      phone: _phoneController.text.trim(),
      role: _selectedRole,
      commissionRate:
          _commissionRateController.text.trim().isEmpty
              ? null
              : double.parse(_commissionRateController.text.trim()),
    );

    final success = await _controller.createUser(request);
    if (success) {
      Get.back();
    }
  }
}
