import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/core/constants/app_strings.dart';
import 'package:myrunway/core/constants/user_roles.dart';
import 'package:myrunway/controllers/user_controller.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/pages/companies/companies_list_page.dart';
import 'package:myrunway/pages/orders/orders_list_page.dart';
import 'package:myrunway/pages/users/users_list_page.dart';

class MainDrawer extends StatelessWidget {
  const MainDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final UserController userController = Get.find<UserController>();
    final AuthService authService = Get.find<AuthService>();

    return Drawer(
      child: Column(
        children: [
          // User Header
          Obx(() => _buildUserHeader(userController)),

          // Navigation Items
          Expanded(
            child: Obx(
              () => ListView(
                padding: EdgeInsets.zero,
                children: _buildNavigationItems(userController),
              ),
            ),
          ),

          // Logout Section
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(color: AppColors.grey200, width: 1),
              ),
            ),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  Get.back(); // Close drawer
                  _showLogoutDialog(authService);
                },
                icon: const Icon(Icons.logout),
                label: const Text(AppStrings.logout),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserHeader(UserController userController) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(16, 40, 16, 16),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.primary, AppColors.primaryLight],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            radius: 32,
            backgroundColor: Colors.white.withOpacity(0.2),
            child: Text(
              userController.userInitials,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            userController.userDisplayName,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              userController.userRoleDisplay,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (userController.userOfficeName.isNotEmpty) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.business, color: Colors.white70, size: 16),
                const SizedBox(width: 6),
                Expanded(
                  child: Text(
                    userController.userOfficeName,
                    style: const TextStyle(fontSize: 12, color: Colors.white70),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  List<Widget> _buildNavigationItems(UserController userController) {
    final items = <Widget>[];

    // Common items for all users
    items.add(
      _buildDrawerItem(
        icon: Icons.dashboard,
        title: AppStrings.dashboard,
        onTap: () {
          Get.back();
          // Navigate to appropriate dashboard based on role
        },
      ),
    );

    // Role-specific navigation items
    switch (userController.userRole) {
      case UserRole.employee:
        items.addAll(_buildEmployeeItems());
        break;
      case UserRole.manager:
        items.addAll(_buildManagerItems());
        break;
      case UserRole.master:
        items.addAll(_buildMasterItems());
        break;
      case null:
        break;
    }

    // Common items at the bottom
    items.add(const Divider());
    items.add(
      _buildDrawerItem(
        icon: Icons.person,
        title: 'الملف الشخصي',
        onTap: () {
          Get.back();
          // Navigate to profile page
        },
      ),
    );
    items.add(
      _buildDrawerItem(
        icon: Icons.settings,
        title: AppStrings.settings,
        onTap: () {
          Get.back();
          // Navigate to settings page
        },
      ),
    );

    return items;
  }

  List<Widget> _buildEmployeeItems() {
    return [
      _buildDrawerItem(
        icon: Icons.assignment,
        title: 'طلباتي',
        onTap: () {
          Get.back();
          // Navigate to employee orders
        },
      ),
      _buildDrawerItem(
        icon: Icons.analytics,
        title: 'تقاريري',
        onTap: () {
          Get.back();
          // Navigate to employee reports
        },
      ),
      _buildDrawerItem(
        icon: Icons.camera_alt,
        title: 'إثبات التسليم',
        onTap: () {
          Get.back();
          // Navigate to proof of delivery
        },
      ),
    ];
  }

  List<Widget> _buildManagerItems() {
    return [
      _buildDrawerItem(
        icon: Icons.assignment,
        title: AppStrings.orders,
        onTap: () {
          Get.back();
          Get.to(() => const OrdersListPage());
        },
      ),
      _buildDrawerItem(
        icon: Icons.people,
        title: AppStrings.employees,
        onTap: () {
          Get.back();
          Get.to(() => const UsersListPage());
        },
      ),
      _buildDrawerItem(
        icon: Icons.business,
        title: AppStrings.clients,
        onTap: () {
          Get.back();
          Get.to(() => const CompaniesListPage());
        },
      ),
      _buildDrawerItem(
        icon: Icons.analytics,
        title: AppStrings.reports,
        onTap: () {
          Get.back();
          // Navigate to reports
        },
      ),
    ];
  }

  List<Widget> _buildMasterItems() {
    return [
      _buildDrawerItem(
        icon: Icons.business_center,
        title: AppStrings.offices,
        onTap: () {
          Get.back();
          // Navigate to offices management
        },
      ),
      _buildDrawerItem(
        icon: Icons.assignment,
        title: AppStrings.orders,
        onTap: () {
          Get.back();
          Get.to(() => const OrdersListPage());
        },
      ),
      _buildDrawerItem(
        icon: Icons.people,
        title: AppStrings.employees,
        onTap: () {
          Get.back();
          Get.to(() => const UsersListPage());
        },
      ),
      _buildDrawerItem(
        icon: Icons.business,
        title: AppStrings.clients,
        onTap: () {
          Get.back();
          Get.to(() => const CompaniesListPage());
        },
      ),
      _buildDrawerItem(
        icon: Icons.analytics,
        title: AppStrings.reports,
        onTap: () {
          Get.back();
          // Navigate to advanced reports
        },
      ),
      const Divider(),
      _buildDrawerItem(
        icon: Icons.admin_panel_settings,
        title: 'إعدادات النظام',
        onTap: () {
          Get.back();
          // Navigate to system settings
        },
      ),
    ];
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? iconColor,
    Color? textColor,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: iconColor ?? AppColors.textSecondary,
        size: 22,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: textColor ?? AppColors.textPrimary,
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      hoverColor: AppColors.primary.withOpacity(0.1),
    );
  }

  void _showLogoutDialog(AuthService authService) {
    Get.dialog(
      AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              Get.back(); // Close dialog
              authService.logout();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }
}
